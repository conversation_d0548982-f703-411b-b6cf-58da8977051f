import { useContext, useState, useMemo, useEffect } from "react";
import { TrackSheetContext } from "./TrackSheetContext";
import DataGridTableTrackSheet from "@/app/_component/DataGridTableTrackSheet";
import { trackSheets_routes } from "@/lib/routePath";
import { getAllData } from "@/lib/helpers";
import Column from "./column";

const ViewTrackSheet = ({
  permissions,
  totalPages,
  customFieldsMap,
  selectedClients,
  trackSheetData,
  pageSize,
  carrierDataUpdate,
  clientDataUpdate,
  setColumnVisibility,
  showStats = true,
}: any) => {
  const { deleteData, setDeletedData } = useContext(TrackSheetContext);

  // Fetch stats from API
  const [stats, setStats] = useState({ totalInvoices: 0, invoicesToday: 0 });
  useEffect(() => {
    if (!showStats) return; // Only fetch stats if showStats is true
    if (!selectedClients || selectedClients.length === 0) {
      return;
    }
    const clientId = selectedClients[0]?.value || selectedClients[0]?.id;
    if (!clientId) {
      return;
    }
    const fetchStats = async () => {
      try {
        const res = await getAllData(
          `${trackSheets_routes.GET_STATS}?clientId=${clientId}`
        );
        setStats(res);
      } catch (e) {
        console.error("Error fetching stats:", e);
        setStats({ totalInvoices: 0, invoicesToday: 0 });
      }
    };
    fetchStats();
  }, [selectedClients, showStats]);
  useEffect(() => {}, [stats]);

  return (
    <div className="w-full">
      {selectedClients.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500 text-lg">Please Select a Client</p>
        </div>
      ) : trackSheetData.data.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500 text-lg">No track sheet data found</p>
        </div>
      ) : (
        <>
          {showStats && selectedClients.length > 0 && (
            <div className="flex flex-wrap gap-6 mb-6">
              <div className="flex items-center bg-white border-l-4 border-gray-800 rounded shadow py-1 px-3 w-64 min-w-[220px]">
                <span
                  className="text-blue-500 text-xl mr-3"
                  role="img"
                  aria-label="invoices"
                >
                  🧾
                </span>
                <div>
                  <div className="text-sm text-gray-500">Total #Invoices</div>
                  <div className="text-2xl font-bold text-gray-900 font-mono tracking-tight">
                    {stats.totalInvoices}
                  </div>
                </div>
              </div>
              <div className="flex items-center bg-white border-l-4 border-gray-800 rounded shadow py-1 px-3 w-64 min-w-[220px]">
                <span
                  className="text-green-500 text-xl mr-3"
                  role="img"
                  aria-label="today"
                >
                  🧾
                </span>
                <div>
                  <div className="text-sm text-gray-500">Added Today</div>
                  <div className="text-2xl font-bold text-gray-900 font-mono tracking-tight">
                    {stats.invoicesToday}
                  </div>
                </div>
              </div>
            </div>
          )}
          <DataGridTableTrackSheet
            data={trackSheetData.data}
            columns={Column(
              permissions,
              setDeletedData,
              deleteData,
              carrierDataUpdate,
              clientDataUpdate,
              {
                customFieldsMap: customFieldsMap,
                data: trackSheetData.data
              }
            )}
            customFieldsMap={customFieldsMap}
            showColDropDowns
            showPageEntries
            className="w-full"
            total={true}
            pageSize={pageSize}
            totalPages={totalPages}
            onColumnVisibilityChange={setColumnVisibility}
          />
        </>
      )}
    </div>
  );
};

export default ViewTrackSheet;
